using System;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using DriverManagementSystem.Views;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة طباعة مباشرة للمحضر والعقد
    /// </summary>
    public static class DirectPrintService
    {
        /// <summary>
        /// طباعة المحضر والعقد مباشرة
        /// </summary>
        public static void PrintReportAndContract(ReportViewModel viewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء الطباعة المباشرة للمحضر والعقد...");

                if (viewModel?.SelectedVisit == null)
                {
                    MessageBox.Show("يرجى اختيار زيارة ميدانية أولاً", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء مربع حوار الطباعة
                var printDialog = new PrintDialog();
                
                // تحديد إعدادات A4
                try
                {
                    var printQueue = printDialog.PrintQueue;
                    var printTicket = printDialog.PrintTicket;
                    
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                    printTicket.PageOrientation = PageOrientation.Portrait;
                    
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديد إعدادات A4");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد إعدادات الطباعة: {ex.Message}");
                }

                // عرض مربع حوار الطباعة
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء المستند للطباعة
                    var document = CreatePrintDocument(viewModel);
                    
                    if (document != null)
                    {
                        // إنشاء DocumentPaginator من FlowDocument
                        var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;

                        // طباعة المستند
                        printDialog.PrintDocument(paginator, "تقرير الزيارة الميدانية والعقد");
                        
                        MessageBox.Show("تم إرسال التقرير والعقد للطباعة بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        System.Diagnostics.Debug.WriteLine("✅ تم إرسال المستند للطباعة بنجاح");
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء المستند للطباعة", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة المباشرة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء مستند للطباعة يحتوي على المحضر والعقد
        /// </summary>
        private static FlowDocument CreatePrintDocument(ReportViewModel viewModel)
        {
            try
            {
                var document = new FlowDocument();
                document.PageWidth = 793.7; // A4 width in pixels (210mm)
                document.PageHeight = 1122.5; // A4 height in pixels (297mm)
                document.PagePadding = new Thickness(50);
                document.FontFamily = new FontFamily("Arial");
                document.FontSize = 12;
                document.FlowDirection = FlowDirection.RightToLeft;

                // إضافة صفحة المحضر
                AddReportPage(document, viewModel);
                
                // إضافة فاصل صفحة
                document.Blocks.Add(new BlockUIContainer(new Border { Height = 50 }));
                
                // إضافة صفحة العقد
                AddContractPage(document, viewModel);

                return document;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إضافة صفحة المحضر
        /// </summary>
        private static void AddReportPage(FlowDocument document, ReportViewModel viewModel)
        {
            try
            {
                // عنوان المحضر
                var title = new Paragraph(new Run("محضر استخراج عروض الأسعار"))
                {
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20)
                };
                document.Blocks.Add(title);

                // معلومات الزيارة
                var visitInfo = new Paragraph();
                visitInfo.Inlines.Add(new Run($"رقم الزيارة: {viewModel.ReportData.VisitNumber}") { FontWeight = FontWeights.Bold });
                visitInfo.Inlines.Add(new LineBreak());
                visitInfo.Inlines.Add(new Run($"تاريخ التقرير: {viewModel.ReportData.ReportDate}"));
                visitInfo.Inlines.Add(new LineBreak());
                visitInfo.Inlines.Add(new Run($"طبيعة الزيارة: {viewModel.ReportData.VisitNature}"));
                visitInfo.Inlines.Add(new LineBreak());
                visitInfo.Inlines.Add(new Run($"القطاع: {viewModel.ReportData.SectorName}"));
                document.Blocks.Add(visitInfo);

                // جدول المشاريع
                if (viewModel.ReportData.Projects?.Count > 0)
                {
                    var projectsTitle = new Paragraph(new Run("المشاريع:"))
                    {
                        FontWeight = FontWeights.Bold,
                        Margin = new Thickness(0, 20, 0, 10)
                    };
                    document.Blocks.Add(projectsTitle);

                    var projectsList = new List();
                    foreach (var project in viewModel.ReportData.Projects)
                    {
                        var listItem = new ListItem(new Paragraph(new Run($"{project.ProjectNumber} - {project.ProjectName}")));
                        projectsList.ListItems.Add(listItem);
                    }
                    document.Blocks.Add(projectsList);
                }

                // جدول عروض الأسعار
                if (viewModel.ReportData.PriceOffers?.Count > 0)
                {
                    var offersTitle = new Paragraph(new Run("عروض الأسعار:"))
                    {
                        FontWeight = FontWeights.Bold,
                        Margin = new Thickness(0, 20, 0, 10)
                    };
                    document.Blocks.Add(offersTitle);

                    var offersTable = CreateOffersTable(viewModel.ReportData.PriceOffers);
                    document.Blocks.Add(offersTable);
                }

                // نص الرسالة
                if (!string.IsNullOrEmpty(viewModel.ReportData.MessageText))
                {
                    var messageTitle = new Paragraph(new Run("نص الرسالة:"))
                    {
                        FontWeight = FontWeights.Bold,
                        Margin = new Thickness(0, 20, 0, 10)
                    };
                    document.Blocks.Add(messageTitle);

                    var messageText = new Paragraph(new Run(viewModel.ReportData.MessageText))
                    {
                        Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                        Padding = new Thickness(15),
                        Margin = new Thickness(0, 0, 0, 20)
                    };
                    document.Blocks.Add(messageText);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة صفحة المحضر: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة صفحة العقد
        /// </summary>
        private static void AddContractPage(FlowDocument document, ReportViewModel viewModel)
        {
            try
            {
                // فاصل صفحة
                document.Blocks.Add(new BlockUIContainer(new Border { Height = 1, Background = Brushes.Black, Margin = new Thickness(0, 30, 0, 30) }));

                // عنوان العقد
                var contractTitle = new Paragraph(new Run("عقد اتفاق إيجار سيارة"))
                {
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 20, 0, 20)
                };
                document.Blocks.Add(contractTitle);

                // معلومات العقد
                var contractInfo = new Paragraph();
                contractInfo.Inlines.Add(new Run($"رقم العقد/الزيارة: {viewModel.ReportData.VisitNumber}") { FontWeight = FontWeights.Bold });
                contractInfo.Inlines.Add(new LineBreak());
                contractInfo.Inlines.Add(new Run($"تاريخ العقد: {viewModel.ReportData.ContractDate}"));
                contractInfo.Inlines.Add(new LineBreak());
                contractInfo.Inlines.Add(new Run($"مدة الإيجار: {viewModel.ReportData.DaysCount} يوم"));
                contractInfo.Inlines.Add(new LineBreak());
                contractInfo.Inlines.Add(new Run($"من تاريخ: {viewModel.ReportData.DepartureDate} إلى: {viewModel.ReportData.ReturnDate}"));
                document.Blocks.Add(contractInfo);

                // معلومات السائق الفائز
                if (viewModel.ReportData.WinnerDriver != null)
                {
                    var driverTitle = new Paragraph(new Run("بيانات السائق:"))
                    {
                        FontWeight = FontWeights.Bold,
                        Margin = new Thickness(0, 20, 0, 10)
                    };
                    document.Blocks.Add(driverTitle);

                    var driverInfo = new Paragraph();
                    driverInfo.Inlines.Add(new Run($"اسم السائق: {viewModel.ReportData.WinnerDriver.DriverName}") { FontWeight = FontWeights.Bold });
                    driverInfo.Inlines.Add(new LineBreak());
                    driverInfo.Inlines.Add(new Run($"رقم الهوية: {viewModel.ReportData.WinnerDriver.NationalId}"));
                    driverInfo.Inlines.Add(new LineBreak());
                    driverInfo.Inlines.Add(new Run($"مكان الإصدار: {viewModel.ReportData.WinnerDriver.IssueLocation}"));
                    driverInfo.Inlines.Add(new LineBreak());
                    driverInfo.Inlines.Add(new Run($"تاريخ الإصدار: {viewModel.ReportData.WinnerDriver.IssueDate}"));
                    document.Blocks.Add(driverInfo);
                }

                // نص العقد من القالب
                if (viewModel.ContractTemplate != null)
                {
                    if (!string.IsNullOrEmpty(viewModel.ContractTemplate.ContractIntroduction))
                    {
                        var contractIntro = new Paragraph(new Run(viewModel.ContractTemplate.ContractIntroduction))
                        {
                            Margin = new Thickness(0, 20, 0, 10),
                            TextAlignment = TextAlignment.Justify
                        };
                        document.Blocks.Add(contractIntro);
                    }

                    if (!string.IsNullOrEmpty(viewModel.ContractTemplate.FirstPartyTemplate))
                    {
                        var firstParty = new Paragraph(new Run(viewModel.ContractTemplate.FirstPartyTemplate))
                        {
                            Margin = new Thickness(0, 10, 0, 10),
                            TextAlignment = TextAlignment.Justify
                        };
                        document.Blocks.Add(firstParty);
                    }

                    if (!string.IsNullOrEmpty(viewModel.ContractTemplate.SecondPartyTemplate))
                    {
                        var secondParty = new Paragraph(new Run(viewModel.ContractTemplate.SecondPartyTemplate))
                        {
                            Margin = new Thickness(0, 10, 0, 20),
                            TextAlignment = TextAlignment.Justify
                        };
                        document.Blocks.Add(secondParty);
                    }
                }

                // التوقيعات
                var signaturesTitle = new Paragraph(new Run("التوقيعات:"))
                {
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 30, 0, 20)
                };
                document.Blocks.Add(signaturesTitle);

                var signatures = new Paragraph();
                signatures.Inlines.Add(new Run("الطرف الأول: ___________________"));
                signatures.Inlines.Add(new Run("                    "));
                signatures.Inlines.Add(new Run("الطرف الثاني: ___________________"));
                signatures.Inlines.Add(new LineBreak());
                signatures.Inlines.Add(new LineBreak());
                signatures.Inlines.Add(new Run("التاريخ: ___________________"));
                signatures.Inlines.Add(new Run("                    "));
                signatures.Inlines.Add(new Run("الختم: ___________________"));
                document.Blocks.Add(signatures);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة صفحة العقد: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء جدول عروض الأسعار
        /// </summary>
        private static Table CreateOffersTable(System.Collections.ObjectModel.ObservableCollection<DriverManagementSystem.Models.PriceOfferItem> offers)
        {
            try
            {
                var table = new Table();
                table.CellSpacing = 0;
                table.BorderBrush = Brushes.Black;
                table.BorderThickness = new Thickness(1);

                // إنشاء الأعمدة
                table.Columns.Add(new TableColumn { Width = new GridLength(50) }); // الرقم
                table.Columns.Add(new TableColumn { Width = new GridLength(150) }); // اسم السائق
                table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // رقم الهاتف
                table.Columns.Add(new TableColumn { Width = new GridLength(80) }); // السعر
                table.Columns.Add(new TableColumn { Width = new GridLength(80) }); // الحالة

                // إنشاء مجموعة الصفوف
                var rowGroup = new TableRowGroup();

                // صف العناوين
                var headerRow = new TableRow();
                headerRow.Background = new SolidColorBrush(Color.FromRgb(248, 249, 250));

                headerRow.Cells.Add(new TableCell(new Paragraph(new Run("م")) { TextAlignment = TextAlignment.Center, FontWeight = FontWeights.Bold }));
                headerRow.Cells.Add(new TableCell(new Paragraph(new Run("اسم السائق")) { TextAlignment = TextAlignment.Center, FontWeight = FontWeights.Bold }));
                headerRow.Cells.Add(new TableCell(new Paragraph(new Run("رقم الهاتف")) { TextAlignment = TextAlignment.Center, FontWeight = FontWeights.Bold }));
                headerRow.Cells.Add(new TableCell(new Paragraph(new Run("السعر")) { TextAlignment = TextAlignment.Center, FontWeight = FontWeights.Bold }));
                headerRow.Cells.Add(new TableCell(new Paragraph(new Run("الحالة")) { TextAlignment = TextAlignment.Center, FontWeight = FontWeights.Bold }));

                // إضافة حدود للخلايا
                foreach (var cell in headerRow.Cells)
                {
                    cell.BorderBrush = Brushes.Black;
                    cell.BorderThickness = new Thickness(1);
                    cell.Padding = new Thickness(5);
                }

                rowGroup.Rows.Add(headerRow);

                // صفوف البيانات
                foreach (var offer in offers)
                {
                    var dataRow = new TableRow();

                    dataRow.Cells.Add(new TableCell(new Paragraph(new Run(offer.SerialNumber.ToString())) { TextAlignment = TextAlignment.Center }));
                    dataRow.Cells.Add(new TableCell(new Paragraph(new Run(offer.DriverName ?? "")) { TextAlignment = TextAlignment.Center }));
                    dataRow.Cells.Add(new TableCell(new Paragraph(new Run(offer.PhoneNumber ?? "")) { TextAlignment = TextAlignment.Center }));
                    dataRow.Cells.Add(new TableCell(new Paragraph(new Run(offer.OfferedPrice.ToString("N0"))) { TextAlignment = TextAlignment.Center }));
                    dataRow.Cells.Add(new TableCell(new Paragraph(new Run(offer.Status ?? "")) { TextAlignment = TextAlignment.Center }));

                    // إضافة حدود للخلايا
                    foreach (var cell in dataRow.Cells)
                    {
                        cell.BorderBrush = Brushes.Black;
                        cell.BorderThickness = new Thickness(1);
                        cell.Padding = new Thickness(5);
                    }

                    // تمييز السائق الفائز
                    if (offer.IsWinner)
                    {
                        dataRow.Background = new SolidColorBrush(Color.FromRgb(212, 237, 218));
                    }

                    rowGroup.Rows.Add(dataRow);
                }

                table.RowGroups.Add(rowGroup);
                return table;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول العروض: {ex.Message}");
                return new Table();
            }
        }
    }
}
