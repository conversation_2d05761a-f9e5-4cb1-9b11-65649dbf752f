using System;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Documents;
using DriverManagementSystem.Views;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة طباعة التصميم المعروض كما هو
    /// </summary>
    public static class VisualPrintService
    {
        /// <summary>
        /// طباعة المحضر والعقد بنفس التصميم المعروض
        /// </summary>
        public static void PrintVisualReport(ReportWindow reportWindow)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء طباعة التصميم المعروض...");

                var viewModel = reportWindow.DataContext as ReportViewModel;
                if (viewModel?.SelectedVisit == null)
                {
                    MessageBox.Show("يرجى اختيار زيارة ميدانية أولاً", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء مربع حوار الطباعة
                var printDialog = new PrintDialog();
                
                // تحديد إعدادات A4
                try
                {
                    var printTicket = printDialog.PrintTicket;
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                    printTicket.PageOrientation = PageOrientation.Portrait;
                    
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديد إعدادات A4");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد إعدادات الطباعة: {ex.Message}");
                }

                // عرض مربع حوار الطباعة
                if (printDialog.ShowDialog() == true)
                {
                    // البحث عن ReportView داخل ReportWindow
                    var reportView = FindReportView(reportWindow);
                    if (reportView != null)
                    {
                        // طباعة المحضر في صفحة منفصلة
                        PrintSeparatePage(printDialog, reportView, "محضر الزيارة الميدانية");

                        // البحث عن ContractsView إذا كان موجود
                        var contractsView = FindContractsView(reportWindow);
                        if (contractsView != null)
                        {
                            // إنشاء مربع حوار طباعة جديد للعقد
                            var contractPrintDialog = new PrintDialog();
                            if (contractPrintDialog.ShowDialog() == true)
                            {
                                // طباعة العقد في صفحة منفصلة
                                PrintSeparatePage(contractPrintDialog, contractsView, "عقد الإيجار");
                            }
                        }

                        MessageBox.Show("تم إرسال التقرير والعقد للطباعة بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        System.Diagnostics.Debug.WriteLine("✅ تم إرسال التصميم للطباعة بنجاح");
                    }
                    else
                    {
                        MessageBox.Show("لم يتم العثور على التقرير للطباعة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة عنصر في صفحة منفصلة تماماً
        /// </summary>
        private static void PrintSeparatePage(PrintDialog printDialog, FrameworkElement element, string documentName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🖨️ بدء طباعة {documentName} في صفحة منفصلة...");

                // إخفاء العناصر التفاعلية قبل الطباعة
                HideInteractiveElements(element);

                try
                {
                    // حفظ الحجم الأصلي
                    var originalWidth = element.Width;
                    var originalHeight = element.Height;
                    var originalActualWidth = element.ActualWidth;
                    var originalActualHeight = element.ActualHeight;

                    // تحديد حجم الصفحة A4 بدقة
                    var pageWidth = 793.7; // A4 width في pixels (210mm)
                    var pageHeight = 1122.5; // A4 height في pixels (297mm)

                    // تعيين حجم العنصر ليملأ الصفحة
                    element.Width = pageWidth;
                    element.Height = pageHeight;

                    // تحديث التخطيط
                    element.UpdateLayout();
                    element.Measure(new Size(pageWidth, pageHeight));
                    element.Arrange(new Rect(0, 0, pageWidth, pageHeight));
                    element.UpdateLayout();

                    // طباعة العنصر مباشرة
                    printDialog.PrintVisual(element, documentName);

                    // استعادة الحجم الأصلي
                    element.Width = originalWidth;
                    element.Height = originalHeight;
                    element.UpdateLayout();

                    System.Diagnostics.Debug.WriteLine($"✅ تم طباعة {documentName} في صفحة منفصلة بنجاح");
                }
                finally
                {
                    // إظهار العناصر التفاعلية مرة أخرى
                    ShowInteractiveElements(element);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في طباعة {documentName}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء مستند مدمج يحتوي على المحضر والعقد في صفحات منفصلة
        /// </summary>
        private static System.Windows.Documents.FlowDocument CreateCombinedDocument(ReportWindow reportWindow)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إنشاء المستند المدمج...");

                var document = new System.Windows.Documents.FlowDocument();
                document.PageWidth = 793.7; // A4 width in pixels (210mm)
                document.PageHeight = 1122.5; // A4 height in pixels (297mm)
                document.PagePadding = new Thickness(50);
                document.FontFamily = new FontFamily("Arial");
                document.FontSize = 12;
                document.FlowDirection = FlowDirection.RightToLeft;

                // البحث عن ReportView
                var reportView = FindReportView(reportWindow);
                if (reportView != null)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على ReportView");

                    // إخفاء العناصر التفاعلية مؤقتاً
                    HideInteractiveElements(reportView);

                    try
                    {
                        // تحديث التخطيط
                        reportView.UpdateLayout();
                        reportView.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                        reportView.Arrange(new Rect(reportView.DesiredSize));
                        reportView.UpdateLayout();

                        // إنشاء صورة من ReportView
                        var reportBitmap = CreateVisualBitmap(reportView);
                        if (reportBitmap != null)
                        {
                            var reportImage = new System.Windows.Controls.Image
                            {
                                Source = reportBitmap,
                                Stretch = Stretch.Uniform,
                                HorizontalAlignment = HorizontalAlignment.Center
                            };

                            // إضافة صورة المحضر إلى المستند
                            var reportContainer = new System.Windows.Documents.BlockUIContainer(reportImage);
                            document.Blocks.Add(reportContainer);

                            System.Diagnostics.Debug.WriteLine("✅ تم إضافة المحضر إلى المستند");
                        }
                    }
                    finally
                    {
                        // إظهار العناصر التفاعلية مرة أخرى
                        ShowInteractiveElements(reportView);
                    }
                }

                // إضافة فاصل صفحة
                document.Blocks.Add(new System.Windows.Documents.Section(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run("")))
                {
                    BreakPageBefore = true
                });

                // البحث عن ContractsView
                var contractsView = FindContractsView(reportWindow);
                if (contractsView != null)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على ContractsView");

                    // إخفاء العناصر التفاعلية مؤقتاً
                    HideInteractiveElements(contractsView);

                    try
                    {
                        // تحديث التخطيط
                        contractsView.UpdateLayout();
                        contractsView.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                        contractsView.Arrange(new Rect(contractsView.DesiredSize));
                        contractsView.UpdateLayout();

                        // إنشاء صورة من ContractsView
                        var contractBitmap = CreateVisualBitmap(contractsView);
                        if (contractBitmap != null)
                        {
                            var contractImage = new System.Windows.Controls.Image
                            {
                                Source = contractBitmap,
                                Stretch = Stretch.Uniform,
                                HorizontalAlignment = HorizontalAlignment.Center
                            };

                            // إضافة صورة العقد إلى المستند
                            var contractContainer = new System.Windows.Documents.BlockUIContainer(contractImage);
                            document.Blocks.Add(contractContainer);

                            System.Diagnostics.Debug.WriteLine("✅ تم إضافة العقد إلى المستند");
                        }
                    }
                    finally
                    {
                        // إظهار العناصر التفاعلية مرة أخرى
                        ShowInteractiveElements(contractsView);
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المستند المدمج بنجاح");
                return document;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند المدمج: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صورة من عنصر مرئي
        /// </summary>
        private static RenderTargetBitmap CreateVisualBitmap(FrameworkElement element)
        {
            try
            {
                var width = (int)element.ActualWidth;
                var height = (int)element.ActualHeight;

                if (width <= 0 || height <= 0)
                {
                    width = (int)element.DesiredSize.Width;
                    height = (int)element.DesiredSize.Height;
                }

                if (width <= 0 || height <= 0)
                {
                    width = 800;
                    height = 600;
                }

                var bitmap = new RenderTargetBitmap(width, height, 96, 96, PixelFormats.Pbgra32);
                bitmap.Render(element);

                return bitmap;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصورة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// طباعة عنصر مرئي محدد
        /// </summary>
        private static void PrintVisualElement(PrintDialog printDialog, FrameworkElement element, string documentName)
        {
            try
            {
                // التأكد من أن العنصر محدث ومرسوم
                element.UpdateLayout();
                element.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                element.Arrange(new Rect(element.DesiredSize));
                element.UpdateLayout();

                // إخفاء الأزرار والعناصر التفاعلية قبل الطباعة
                HideInteractiveElements(element);

                try
                {
                    // طباعة العنصر مباشرة
                    printDialog.PrintVisual(element, documentName);
                    System.Diagnostics.Debug.WriteLine($"✅ تم طباعة {documentName}");
                }
                finally
                {
                    // إظهار الأزرار والعناصر التفاعلية مرة أخرى
                    ShowInteractiveElements(element);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في طباعة {documentName}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// البحث عن ReportView داخل النافذة
        /// </summary>
        private static ReportView FindReportView(DependencyObject parent)
        {
            try
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    
                    if (child is ReportView reportView)
                    {
                        return reportView;
                    }
                    
                    var result = FindReportView(child);
                    if (result != null)
                    {
                        return result;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن ReportView: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// البحث عن ContractsView داخل النافذة
        /// </summary>
        private static ContractsView FindContractsView(DependencyObject parent)
        {
            try
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    
                    if (child is ContractsView contractsView)
                    {
                        return contractsView;
                    }
                    
                    var result = FindContractsView(child);
                    if (result != null)
                    {
                        return result;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن ContractsView: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إخفاء العناصر التفاعلية قبل الطباعة
        /// </summary>
        private static void HideInteractiveElements(DependencyObject parent)
        {
            try
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    
                    // إخفاء الأزرار
                    if (child is Button button)
                    {
                        button.Visibility = Visibility.Collapsed;
                    }
                    // إخفاء أشرطة التمرير
                    else if (child is ScrollBar scrollBar)
                    {
                        scrollBar.Visibility = Visibility.Collapsed;
                    }
                    // إخفاء عناصر التحكم الأخرى
                    else if (child is Slider slider)
                    {
                        slider.Visibility = Visibility.Collapsed;
                    }
                    
                    // البحث في العناصر الفرعية
                    HideInteractiveElements(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إخفاء العناصر التفاعلية: {ex.Message}");
            }
        }

        /// <summary>
        /// إظهار العناصر التفاعلية بعد الطباعة
        /// </summary>
        private static void ShowInteractiveElements(DependencyObject parent)
        {
            try
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    
                    // إظهار الأزرار
                    if (child is Button button)
                    {
                        button.Visibility = Visibility.Visible;
                    }
                    // إظهار أشرطة التمرير
                    else if (child is ScrollBar scrollBar)
                    {
                        scrollBar.Visibility = Visibility.Visible;
                    }
                    // إظهار عناصر التحكم الأخرى
                    else if (child is Slider slider)
                    {
                        slider.Visibility = Visibility.Visible;
                    }
                    
                    // البحث في العناصر الفرعية
                    ShowInteractiveElements(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إظهار العناصر التفاعلية: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة نافذة كاملة بجميع محتوياتها
        /// </summary>
        public static void PrintEntireWindow(ReportWindow reportWindow)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء طباعة النافذة الكاملة...");

                var printDialog = new PrintDialog();
                
                if (printDialog.ShowDialog() == true)
                {
                    // إخفاء العناصر التفاعلية
                    HideInteractiveElements(reportWindow);
                    
                    try
                    {
                        // طباعة النافذة كاملة
                        printDialog.PrintVisual(reportWindow, "تقرير الزيارة الميدانية والعقد");
                        
                        MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    finally
                    {
                        // إظهار العناصر التفاعلية مرة أخرى
                        ShowInteractiveElements(reportWindow);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في طباعة النافذة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
