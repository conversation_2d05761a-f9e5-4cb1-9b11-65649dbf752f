using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Printing;
using DriverManagementSystem.Views;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة الطباعة الاحترافية الجديدة - تضمن نقل البيانات بشكل صحيح
    /// </summary>
    public static class ProfessionalPrintService
    {
        /// <summary>
        /// طباعة التقرير الكامل مع جميع الصفحات والبيانات
        /// </summary>
        public static void PrintCompleteReport(ReportViewModel viewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء الطباعة الاحترافية...");

                if (viewModel?.ReportData == null)
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء حوار الطباعة
                var printDialog = new PrintDialog();
                
                // تحديد إعدادات A4
                SetupA4PrintSettings(printDialog);

                // عرض حوار اختيار الطابعة
                if (printDialog.ShowDialog() == true)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم اختيار الطابعة، بدء إنشاء المستند...");
                    
                    // إنشاء مستند الطباعة مع البيانات الفعلية
                    var printDocument = CreatePrintDocument(viewModel);
                    
                    if (printDocument != null)
                    {
                        // طباعة المستند
                        printDialog.PrintDocument(printDocument.DocumentPaginator, "تقرير الزيارة الميدانية");
                        
                        MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        System.Diagnostics.Debug.WriteLine("✅ تمت الطباعة بنجاح");
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء مستند الطباعة", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديد إعدادات الطباعة A4
        /// </summary>
        private static void SetupA4PrintSettings(PrintDialog printDialog)
        {
            try
            {
                var a4Size = new PageMediaSize(PageMediaSizeName.ISOA4, 210, 297);
                printDialog.PrintTicket.PageMediaSize = a4Size;
                printDialog.PrintTicket.PageOrientation = PageOrientation.Portrait;
                
                System.Diagnostics.Debug.WriteLine("✅ تم تحديد إعدادات A4");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد إعدادات A4: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء مستند الطباعة مع البيانات الفعلية
        /// </summary>
        private static FixedDocument CreatePrintDocument(ReportViewModel viewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء مستند الطباعة...");
                
                var document = new FixedDocument();
                
                // إنشاء صفحة التقرير الرئيسية
                var reportPage = CreateReportPage(viewModel);
                if (reportPage != null)
                {
                    document.Pages.Add(reportPage);
                    System.Diagnostics.Debug.WriteLine("✅ تمت إضافة صفحة التقرير");
                }

                // إنشاء صفحات العقد
                var contractPages = CreateContractPages(viewModel);
                foreach (var contractPage in contractPages)
                {
                    document.Pages.Add(contractPage);
                }
                
                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مستند بـ {document.Pages.Count} صفحة");
                return document;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة التقرير الرئيسية مع البيانات
        /// </summary>
        private static PageContent CreateReportPage(ReportViewModel viewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 إنشاء صفحة التقرير مع البيانات...");
                
                // إنشاء صفحة ثابتة
                var fixedPage = new FixedPage
                {
                    Width = 816,  // A4 width at 96 DPI
                    Height = 1056, // A4 height at 96 DPI
                    Background = Brushes.White
                };

                // إنشاء محتوى التقرير مع البيانات الفعلية
                var reportContent = CreateReportContent(viewModel);
                fixedPage.Children.Add(reportContent);

                // إنشاء PageContent
                var pageContent = new PageContent();
                ((IAddChild)pageContent).AddChild(fixedPage);
                
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة التقرير");
                return pageContent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة التقرير: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء محتوى التقرير مع البيانات الفعلية
        /// </summary>
        private static FrameworkElement CreateReportContent(ReportViewModel viewModel)
        {
            var container = new Grid
            {
                Width = 776, // مع هوامش
                Height = 1016,
                Background = Brushes.White,
                Margin = new Thickness(20)
            };

            container.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // العنوان
            container.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // معلومات الزيارة
            container.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // جدول المشاريع
            container.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // جدول عروض الأسعار
            container.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // المساحة المتبقية

            // العنوان الرئيسي
            var title = new TextBlock
            {
                Text = "محضر استدراج عروض اسعار",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20),
                TextAlignment = TextAlignment.Center
            };
            Grid.SetRow(title, 0);
            container.Children.Add(title);

            // معلومات الزيارة
            var visitInfo = CreateVisitInfoSection(viewModel);
            Grid.SetRow(visitInfo, 1);
            container.Children.Add(visitInfo);

            // جدول المشاريع
            var projectsTable = CreateProjectsTable(viewModel);
            Grid.SetRow(projectsTable, 2);
            container.Children.Add(projectsTable);

            // جدول عروض الأسعار
            var offersTable = CreateOffersTable(viewModel);
            Grid.SetRow(offersTable, 3);
            container.Children.Add(offersTable);

            return container;
        }

        /// <summary>
        /// إنشاء قسم معلومات الزيارة
        /// </summary>
        private static FrameworkElement CreateVisitInfoSection(ReportViewModel viewModel)
        {
            var grid = new Grid { Margin = new Thickness(0, 0, 0, 20) };
            
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // رقم الزيارة
            var visitNumberLabel = new TextBlock
            {
                Text = $"رقم الزيارة: {viewModel.ReportData.VisitNumber}",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(5)
            };
            Grid.SetRow(visitNumberLabel, 0);
            Grid.SetColumn(visitNumberLabel, 0);
            grid.Children.Add(visitNumberLabel);

            // التاريخ
            var dateLabel = new TextBlock
            {
                Text = $"التاريخ: {viewModel.ReportData.Date}",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(5)
            };
            Grid.SetRow(dateLabel, 0);
            Grid.SetColumn(dateLabel, 1);
            grid.Children.Add(dateLabel);

            // القطاع
            var sectorLabel = new TextBlock
            {
                Text = $"القطاع: {viewModel.ReportData.SectorName}",
                FontSize = 14,
                Margin = new Thickness(5)
            };
            Grid.SetRow(sectorLabel, 1);
            Grid.SetColumn(sectorLabel, 0);
            grid.Children.Add(sectorLabel);

            // مسؤول الحركة
            var officerLabel = new TextBlock
            {
                Text = $"مسؤول الحركة: {viewModel.ReportData.MovementOfficer}",
                FontSize = 14,
                Margin = new Thickness(5)
            };
            Grid.SetRow(officerLabel, 1);
            Grid.SetColumn(officerLabel, 1);
            grid.Children.Add(officerLabel);

            return grid;
        }

        /// <summary>
        /// إنشاء جدول المشاريع مع البيانات الفعلية
        /// </summary>
        private static FrameworkElement CreateProjectsTable(ReportViewModel viewModel)
        {
            var container = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };

            // عنوان الجدول
            var tableTitle = new TextBlock
            {
                Text = "المشاريع التي سيتم زيارتها",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            container.Children.Add(tableTitle);

            // إنشاء الجدول
            var table = new Grid
            {
                Background = Brushes.White
            };

            // تعريف الأعمدة
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(80) }); // رقم المشروع
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // اسم المشروع
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(80) }); // عدد الأيام

            // رأس الجدول
            AddTableHeader(table, new[] { "رقم المشروع", "اسم المشروع", "عدد الأيام" });

            // إضافة البيانات
            int rowIndex = 1;
            foreach (var project in viewModel.ReportData.Projects)
            {
                table.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                
                AddTableCell(table, project.ProjectNumber, rowIndex, 0);
                AddTableCell(table, project.ProjectName, rowIndex, 1);
                AddTableCell(table, project.SerialNumber.ToString(), rowIndex, 2);
                
                rowIndex++;
            }

            container.Children.Add(table);
            return container;
        }

        /// <summary>
        /// إنشاء جدول عروض الأسعار مع البيانات الفعلية
        /// </summary>
        private static FrameworkElement CreateOffersTable(ReportViewModel viewModel)
        {
            var container = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };

            // عنوان الجدول
            var tableTitle = new TextBlock
            {
                Text = "بيانات الزيارة الميدانية",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            container.Children.Add(tableTitle);

            // إنشاء الجدول
            var table = new Grid
            {
                Background = Brushes.White
            };

            // تعريف الأعمدة
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(60) }); // م
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // اسم السائق
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) }); // رقم الهاتف
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) }); // السعر المعروض
            table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(80) }); // الحالة

            // رأس الجدول
            AddTableHeader(table, new[] { "م", "اسم السائق", "رقم الهاتف", "السعر المعروض", "الحالة" });

            // إضافة البيانات
            int rowIndex = 1;
            foreach (var offer in viewModel.ReportData.PriceOffers)
            {
                table.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                
                AddTableCell(table, offer.SerialNumber.ToString(), rowIndex, 0);
                AddTableCell(table, offer.DriverName, rowIndex, 1);
                AddTableCell(table, offer.PhoneNumber, rowIndex, 2);
                AddTableCell(table, offer.OfferedPrice.ToString("N0"), rowIndex, 3);
                AddTableCell(table, offer.Status, rowIndex, 4);
                
                rowIndex++;
            }

            container.Children.Add(table);
            return container;
        }

        /// <summary>
        /// إضافة رأس الجدول
        /// </summary>
        private static void AddTableHeader(Grid table, string[] headers)
        {
            table.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            
            for (int i = 0; i < headers.Length; i++)
            {
                var headerCell = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(240, 240, 240)),
                    BorderBrush = Brushes.Black,
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(8, 5)
                };

                var headerText = new TextBlock
                {
                    Text = headers[i],
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                headerCell.Child = headerText;
                Grid.SetRow(headerCell, 0);
                Grid.SetColumn(headerCell, i);
                table.Children.Add(headerCell);
            }
        }

        /// <summary>
        /// إضافة خلية في الجدول
        /// </summary>
        private static void AddTableCell(Grid table, string content, int row, int column)
        {
            var cell = new Border
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8, 5)
            };

            var cellText = new TextBlock
            {
                Text = content ?? "",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                TextWrapping = TextWrapping.Wrap
            };

            cell.Child = cellText;
            Grid.SetRow(cell, row);
            Grid.SetColumn(cell, column);
            table.Children.Add(cell);
        }

        /// <summary>
        /// إنشاء صفحات العقد
        /// </summary>
        private static List<PageContent> CreateContractPages(ReportViewModel viewModel)
        {
            var pages = new List<PageContent>();
            
            try
            {
                // إنشاء صفحة العقد الأولى
                var contractPage1 = CreateContractPage1(viewModel);
                if (contractPage1 != null)
                {
                    pages.Add(contractPage1);
                }

                // إنشاء صفحة العقد الثانية
                var contractPage2 = CreateContractPage2(viewModel);
                if (contractPage2 != null)
                {
                    pages.Add(contractPage2);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {pages.Count} صفحة عقد");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحات العقد: {ex.Message}");
            }

            return pages;
        }

        /// <summary>
        /// إنشاء صفحة العقد الأولى
        /// </summary>
        private static PageContent CreateContractPage1(ReportViewModel viewModel)
        {
            try
            {
                var fixedPage = new FixedPage
                {
                    Width = 816,
                    Height = 1056,
                    Background = Brushes.White
                };

                // محتوى صفحة العقد الأولى
                var contractContent = new TextBlock
                {
                    Text = $"عقد اتفاق ايجار سيارة\n\nرقم الزيارة: {viewModel.ReportData.VisitNumber}\nالتاريخ: {viewModel.ReportData.Date}\n\n[محتوى العقد سيتم إضافته لاحقاً]",
                    FontSize = 14,
                    Margin = new Thickness(40),
                    TextWrapping = TextWrapping.Wrap
                };

                fixedPage.Children.Add(contractContent);

                var pageContent = new PageContent();
                ((IAddChild)pageContent).AddChild(fixedPage);
                
                return pageContent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة العقد الأولى: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة العقد الثانية
        /// </summary>
        private static PageContent CreateContractPage2(ReportViewModel viewModel)
        {
            try
            {
                var fixedPage = new FixedPage
                {
                    Width = 816,
                    Height = 1056,
                    Background = Brushes.White
                };

                // محتوى صفحة العقد الثانية
                var contractContent = new TextBlock
                {
                    Text = $"تتمة عقد اتفاق ايجار سيارة\n\nرقم الزيارة: {viewModel.ReportData.VisitNumber}\n\n[محتوى العقد الثاني سيتم إضافته لاحقاً]",
                    FontSize = 14,
                    Margin = new Thickness(40),
                    TextWrapping = TextWrapping.Wrap
                };

                fixedPage.Children.Add(contractContent);

                var pageContent = new PageContent();
                ((IAddChild)pageContent).AddChild(fixedPage);
                
                return pageContent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة العقد الثانية: {ex.Message}");
                return null;
            }
        }
    }
}
