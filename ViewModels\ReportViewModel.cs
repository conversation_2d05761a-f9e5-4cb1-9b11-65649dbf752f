using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Documents;
using System.Printing;
using Microsoft.Win32;
using SFDSystem.Helpers;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;
using DriverManagementSystem.Helpers;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.ViewModels
{
    public class ReportViewModel : BindableBase
    {
        private readonly SqliteDataService _databaseService;
        private readonly DriverDataService _driverDataService;
        private readonly ApplicationDbContext _context;
        private ReportModel _reportData;
        private ObservableCollection<FieldVisit> _fieldVisits;
        private ContractTemplate _contractTemplate;
        private FieldVisit _selectedVisit;

        public ReportViewModel()
        {
            _databaseService = new SqliteDataService();
            _context = new ApplicationDbContext();
            _driverDataService = new DriverDataService(_context);
            _reportData = new ReportModel();
            _fieldVisits = new ObservableCollection<FieldVisit>();

            // Commands - تم حذف جميع أوامر الطباعة القديمة
            LoadDataCommand = new DelegateCommand(LoadData);
            GenerateReportCommand = new DelegateCommand(GenerateReport, CanGenerateReport);

            LoadData();
            LoadContractTemplate();
        }

        #region Properties

        public ReportModel ReportData
        {
            get => _reportData;
            set
            {
                SetProperty(ref _reportData, value);
                UpdateProcessedTexts();
            }
        }

        public ObservableCollection<FieldVisit> FieldVisits
        {
            get => _fieldVisits;
            set => SetProperty(ref _fieldVisits, value);
        }

        public FieldVisit SelectedVisit
        {
            get => _selectedVisit;
            set
            {
                if (SetProperty(ref _selectedVisit, value))
                {
                    GenerateReportCommand.RaiseCanExecuteChanged();
                    if (value != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 تم اختيار الزيارة: {value.VisitNumber} (ID: {value.Id})");
                        LoadVisitData(value);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("🔍 تم إلغاء اختيار الزيارة");
                        ClearReportData();
                        RaisePropertyChanged(nameof(ReportData));
                    }
                }
            }
        }

        public ContractTemplate ContractTemplate
        {
            get => _contractTemplate;
            set
            {
                _contractTemplate = value;
                RaisePropertyChanged();
                UpdateProcessedTexts();
            }
        }

        #endregion

        #region Commands

        public DelegateCommand LoadDataCommand { get; }
        public DelegateCommand GenerateReportCommand { get; }

        #endregion

        #region Methods

        private async void LoadContractTemplate()
        {
            try
            {
                await _context.Database.EnsureCreatedAsync();
                ContractTemplate = await _context.ContractTemplates.FirstOrDefaultAsync();

                if (ContractTemplate == null)
                {
                    ContractTemplate = new ContractTemplate();
                    _context.ContractTemplates.Add(ContractTemplate);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب عقد افتراضي");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل قالب العقد (ID: {ContractTemplate.Id})");
                }

                SaveOriginalTexts();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل قالب العقد: {ex.Message}");
                ContractTemplate = new ContractTemplate();
                SaveOriginalTexts();
            }
        }

        private string _originalContractIntroduction;
        private string _originalFirstPartyTemplate;
        private string _originalSecondPartyTemplate;

        private void SaveOriginalTexts()
        {
            if (ContractTemplate != null)
            {
                _originalContractIntroduction = ContractTemplate.ContractIntroduction;
                _originalFirstPartyTemplate = ContractTemplate.FirstPartyTemplate;
                _originalSecondPartyTemplate = ContractTemplate.SecondPartyTemplate;
                System.Diagnostics.Debug.WriteLine("💾 تم حفظ النصوص الأصلية");
            }
        }

        private async void UpdateProcessedTexts()
        {
            if (ContractTemplate == null || ReportData == null)
                return;

            try
            {
                EnsureOriginalTextsExist();

                ContractTemplate.ContractIntroduction = ProcessContractText(_originalContractIntroduction);
                ContractTemplate.FirstPartyTemplate = ProcessContractText(_originalFirstPartyTemplate);
                ContractTemplate.SecondPartyTemplate = ProcessContractText(_originalSecondPartyTemplate);

                await SaveUpdatedTextsToDatabase();
                RaisePropertyChanged(nameof(ContractTemplate));

                System.Diagnostics.Debug.WriteLine("✅ تم تحديث النصوص المعالجة وحفظها");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة النصوص: {ex.Message}");
            }
        }

        private async Task SaveUpdatedTextsToDatabase()
        {
            try
            {
                using var context = new Data.ApplicationDbContext();
                var existingTemplate = await context.ContractTemplates.FirstOrDefaultAsync();

                if (existingTemplate != null)
                {
                    existingTemplate.ContractIntroduction = ContractTemplate.ContractIntroduction;
                    existingTemplate.FirstPartyTemplate = ContractTemplate.FirstPartyTemplate;
                    existingTemplate.SecondPartyTemplate = ContractTemplate.SecondPartyTemplate;
                    existingTemplate.LastModified = DateTime.Now;

                    context.ContractTemplates.Update(existingTemplate);
                    await context.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine("💾 تم حفظ النصوص المحدثة في قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ النصوص المحدثة: {ex.Message}");
            }
        }

        private void EnsureOriginalTextsExist()
        {
            if (string.IsNullOrEmpty(_originalContractIntroduction))
            {
                _originalContractIntroduction = ContractTemplate.ContractIntroduction;
            }
            if (string.IsNullOrEmpty(_originalFirstPartyTemplate))
            {
                _originalFirstPartyTemplate = ContractTemplate.FirstPartyTemplate;
            }
            if (string.IsNullOrEmpty(_originalSecondPartyTemplate))
            {
                _originalSecondPartyTemplate = ContractTemplate.SecondPartyTemplate;
            }
        }

        public void RestoreOriginalTexts()
        {
            if (ContractTemplate != null)
            {
                ContractTemplate.ContractIntroduction = _originalContractIntroduction ?? ContractTemplate.ContractIntroduction;
                ContractTemplate.FirstPartyTemplate = _originalFirstPartyTemplate ?? ContractTemplate.FirstPartyTemplate;
                ContractTemplate.SecondPartyTemplate = _originalSecondPartyTemplate ?? ContractTemplate.SecondPartyTemplate;

                RaisePropertyChanged(nameof(ContractTemplate));
                System.Diagnostics.Debug.WriteLine("🔄 تم استعادة النصوص الأصلية");
            }
        }

        private string ProcessContractText(string template)
        {
            if (string.IsNullOrEmpty(template) || ReportData == null)
                return template ?? "";

            var processedText = template;

            try
            {
                DateTime contractDate = DateTime.Now;

                if (!string.IsNullOrEmpty(ReportData.VisitNumber))
                {
                    try
                    {
                        using var context = new Data.ApplicationDbContext();
                        var visit = context.FieldVisits.FirstOrDefault(v => v.VisitNumber == ReportData.VisitNumber);
                        if (visit != null)
                        {
                            contractDate = visit.AddDate;
                            System.Diagnostics.Debug.WriteLine($"📅 تم استخدام تاريخ الإضافة: {contractDate:dd/MM/yyyy}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب تاريخ الإضافة: {ex.Message}");
                    }
                }

                var contractDateString = contractDate.ToString("dd/MM/yyyy");
                var contractDayName = DateHelper.GetArabicDayName(contractDate);

                var startDate = ReportData.DepartureDate ?? DateTime.Now.ToString("dd/MM/yyyy");
                var endDate = ReportData.ReturnDate ?? DateTime.Now.ToString("dd/MM/yyyy");
                var startDayName = DateHelper.GetArabicDayName(startDate);
                var endDayName = DateHelper.GetArabicDayName(endDate);

                // استبدال placeholders
                processedText = processedText.Replace("{StartDate}", startDate);
                processedText = processedText.Replace("{EndDate}", endDate);
                processedText = processedText.Replace("{StartDateArabic}", startDayName);
                processedText = processedText.Replace("{EndDateArabic}", endDayName);
                processedText = processedText.Replace("{ContractDate}", contractDateString);
                processedText = processedText.Replace("{ContractDateArabic}", contractDayName);

                // بيانات السائق الفائز
                if (ReportData.WinnerDriver != null)
                {
                    processedText = processedText.Replace("{DriverName}", ReportData.WinnerDriver.DriverName ?? "");
                    processedText = processedText.Replace("{NationalId}", ReportData.WinnerDriver.NationalId ?? "");
                    processedText = processedText.Replace("{IssuePlace}", ReportData.WinnerDriver.IssueLocation ?? "");
                    processedText = processedText.Replace("{IssueDate}", ReportData.WinnerDriver.IssueDate ?? "");
                }

                processedText = processedText.Replace("{VisitNumber}", ReportData.VisitNumber ?? "");
                processedText = processedText.Replace("{DaysCount}", ReportData.DaysCount.ToString());

                return processedText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة النص: {ex.Message}");
                return template;
            }
        }

        private async void LoadData()
        {
            try
            {
                var visits = await _databaseService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits.OrderByDescending(v => v.CreatedAt))
                {
                    FieldVisits.Add(visit);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateReport()
        {
            try
            {
                if (SelectedVisit == null)
                {
                    MessageBox.Show("يرجى اختيار زيارة ميدانية أولاً", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء التقرير بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool CanGenerateReport()
        {
            return SelectedVisit != null;
        }

        private async void LoadVisitData(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تحميل بيانات الزيارة: {visit.VisitNumber} (ID: {visit.Id})");

                ClearReportData();
                RaisePropertyChanged(nameof(ReportData));

                // تحديث البيانات الأساسية
                ReportData.VisitNumber = visit.VisitNumber;
                ReportData.ReportDate = visit.AddDate.ToString("dd/MM/yyyy");
                ReportData.VisitNature = visit.MissionPurpose;
                ReportData.DepartureDate = visit.DepartureDate.ToString("dd/MM/yyyy");
                ReportData.ReturnDate = visit.ReturnDate.ToString("dd/MM/yyyy");
                ReportData.DaysCount = visit.DaysCount;
                ReportData.SectorName = visit.SectorName;
                ReportData.Notes = visit.VisitNotes ?? "لا توجد ملاحظات إضافية";

                // تحديث بيانات التاريخ واليوم للعقد
                ReportData.ContractDate = visit.AddDate.ToString("dd/MM/yyyy");
                ReportData.ContractDayName = Helpers.DateHelper.GetArabicDayName(visit.AddDate);
                ReportData.StartDateArabic = Helpers.DateHelper.GetArabicDayName(visit.DepartureDate);
                ReportData.EndDateArabic = Helpers.DateHelper.GetArabicDayName(visit.ReturnDate);

                // تحميل البيانات الفرعية
                LoadVisitorsData(visit);
                LoadItineraryData(visit);
                await LoadProjectsDataAsync(visit);
                await LoadDriversAndPricesData(visit);
                await LoadWinnerDriverData(visit);
                await LoadSelectedVehicleData(visit);
                LoadSignaturesData(visit);
                GenerateMessageText();
                LoadWinnerDriverMessage(visit);
                await LoadDocumentationImages(visit);

                // تحديث النصوص المعالجة للعقد
                UpdateProcessedTexts();
                RaisePropertyChanged(nameof(ReportData));
                GenerateReportCommand.RaiseCanExecuteChanged();

                System.Diagnostics.Debug.WriteLine("🎯 تم الانتهاء من تحميل جميع البيانات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات الزيارة: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل بيانات الزيارة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearReportData()
        {
            ReportData.VisitNumber = string.Empty;
            ReportData.ReportDate = string.Empty;
            ReportData.VisitNature = string.Empty;
            ReportData.VisitConductor = string.Empty;
            ReportData.RouteDescription = string.Empty;
            ReportData.DepartureDate = string.Empty;
            ReportData.ReturnDate = string.Empty;
            ReportData.DaysCount = 0;
            ReportData.SectorName = string.Empty;
            ReportData.Notes = string.Empty;

            ReportData.ContractDate = string.Empty;
            ReportData.ContractDayName = string.Empty;
            ReportData.StartDateArabic = string.Empty;
            ReportData.EndDateArabic = string.Empty;

            ReportData.DocumentationImage1 = string.Empty;
            ReportData.DocumentationImage2 = string.Empty;
            ReportData.DocumentationImage3 = string.Empty;
            ReportData.DocumentationImage4 = string.Empty;

            ReportData.Projects.Clear();
            ReportData.PriceOffers.Clear();
            ReportData.SelectedDriverName = string.Empty;
            ReportData.SelectedDriverPhone = string.Empty;
            ReportData.VehicleType = string.Empty;
            ReportData.VehicleModel = string.Empty;
            ReportData.VehicleColor = string.Empty;
            ReportData.PlateNumber = string.Empty;

            RestoreOriginalTexts();
        }

        private void LoadVisitorsData(FieldVisit visit)
        {
            try
            {
                if (visit.Visitors?.Any() == true)
                {
                    var visitorNames = visit.Visitors
                        .Where(v => !string.IsNullOrWhiteSpace(v.OfficerName))
                        .Select(v => v.OfficerName)
                        .ToList();

                    ReportData.VisitConductor = visitorNames.Any() ? string.Join(" - ", visitorNames) : "غير محدد";
                }
                else
                {
                    ReportData.VisitConductor = "غير محدد";
                }
            }
            catch (Exception ex)
            {
                ReportData.VisitConductor = "غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading visitors: {ex.Message}");
            }
        }

        private void LoadItineraryData(FieldVisit visit)
        {
            try
            {
                if (visit.Itinerary?.Any() == true)
                {
                    var itineraryItems = visit.Itinerary
                        .Where(item => !string.IsNullOrWhiteSpace(item))
                        .ToList();

                    ReportData.RouteDescription = itineraryItems.Any() ? string.Join(" // ", itineraryItems) : "خط السير غير محدد";
                }
                else
                {
                    ReportData.RouteDescription = "خط السير غير محدد";
                }
            }
            catch (Exception ex)
            {
                ReportData.RouteDescription = "خط السير غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading itinerary: {ex.Message}");
            }
        }

        private async Task LoadProjectsDataAsync(FieldVisit visit)
        {
            try
            {
                ReportData.Projects.Clear();
                System.Diagnostics.Debug.WriteLine($"🔍 بدء تحميل المشاريع للزيارة: {visit.VisitNumber}");

                if (visit.Projects?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {visit.Projects.Count} مشروع");

                    for (int i = 0; i < visit.Projects.Count; i++)
                    {
                        var project = visit.Projects[i];
                        var reportItem = new ProjectReportItem
                        {
                            SerialNumber = i + 1,
                            ProjectNumber = !string.IsNullOrWhiteSpace(project.ProjectNumber) ? project.ProjectNumber : "-",
                            ProjectName = !string.IsNullOrWhiteSpace(project.ProjectName) ? project.ProjectName : "-"
                        };

                        ReportData.Projects.Add(reportItem);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة المشروع: {reportItem.SerialNumber} - {reportItem.ProjectNumber} - {reportItem.ProjectName}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مشاريع في الزيارة المختارة");
                    ReportData.Projects.Add(new ProjectReportItem
                    {
                        SerialNumber = 1,
                        ProjectNumber = "لا توجد مشاريع",
                        ProjectName = "لم يتم تحديد مشاريع لهذه الزيارة"
                    });
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {ReportData.Projects.Count} عنصر مشروع بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المشاريع: {ex.Message}");
                ReportData.Projects.Clear();
                ReportData.Projects.Add(new ProjectReportItem
                {
                    SerialNumber = 1,
                    ProjectNumber = "خطأ في التحميل",
                    ProjectName = $"خطأ في تحميل المشاريع: {ex.Message}"
                });
            }
        }

        private async Task LoadDriversAndPricesData(FieldVisit visit)
        {
            try
            {
                ReportData.PriceOffers.Clear();

                // محاولة جلب البيانات من مصادر متعددة
                var realOffers = await LoadRealOffersFromDatabase(visit.VisitNumber);
                if (realOffers?.Any() == true)
                {
                    foreach (var offer in realOffers)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {realOffers.Count} عرض سعر حقيقي");
                    return;
                }

                var offersFromVisit = LoadOffersFromVisitData(visit);
                if (offersFromVisit?.Any() == true)
                {
                    foreach (var offer in offersFromVisit)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {offersFromVisit.Count} عرض سعر من بيانات الزيارة");
                    return;
                }

                var priceOffers = await _driverDataService.GetEnhancedPriceOffersAsync(visit.VisitNumber);
                if (priceOffers?.Any() == true)
                {
                    foreach (var offer in priceOffers)
                    {
                        ReportData.PriceOffers.Add(offer);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {priceOffers.Count} عرض سعر من جدول العروض");
                    return;
                }

                // لا توجد عروض
                System.Diagnostics.Debug.WriteLine($"⚠️ لم توجد عروض للزيارة {visit.VisitNumber}");
                ReportData.PriceOffers.Add(new PriceOfferItem
                {
                    SerialNumber = 1,
                    DriverName = $"لا توجد عروض أسعار للزيارة {visit.VisitNumber}",
                    PhoneNumber = "---",
                    OfferedPrice = 0,
                    Status = "لم يتم تقديم عروض",
                    IsWinner = false
                });
            }
            catch (Exception ex)
            {
                ReportData.PriceOffers.Clear();
                ReportData.PriceOffers.Add(new PriceOfferItem
                {
                    SerialNumber = 1,
                    DriverName = "خطأ في تحميل البيانات",
                    PhoneNumber = "غير محدد",
                    OfferedPrice = 0,
                    Status = "خطأ",
                    IsWinner = false
                });
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل عروض الأسعار: {ex.Message}");
            }
        }

        private async Task<List<PriceOfferItem>> LoadRealOffersFromDatabase(string visitNumber)
        {
            try
            {
                using var context = new ApplicationDbContext();
                // البحث في DriverQuotes بناءً على DriverName أو DriverCode
                var quotes = await context.DriverQuotes
                    .Where(q => q.DriverName.Contains(visitNumber) || q.DriverCode.Contains(visitNumber))
                    .ToListAsync();

                if (quotes?.Any() == true)
                {
                    var offers = new List<PriceOfferItem>();
                    for (int i = 0; i < quotes.Count; i++)
                    {
                        var quote = quotes[i];
                        offers.Add(new PriceOfferItem
                        {
                            SerialNumber = i + 1,
                            DriverName = quote.DriverName ?? "غير محدد",
                            PhoneNumber = quote.PhoneNumber ?? "غير محدد",
                            OfferedPrice = quote.QuotedPrice,
                            Status = quote.Status == QuoteStatus.Accepted ? "مقبول" : "مرفوض",
                            IsWinner = quote.IsSelected
                        });
                    }
                    return offers;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب العروض الحقيقية: {ex.Message}");
            }
            return null;
        }

        private List<PriceOfferItem> LoadOffersFromVisitData(FieldVisit visit)
        {
            try
            {
                if (string.IsNullOrEmpty(visit.SelectedDrivers))
                    return null;

                var offers = new List<PriceOfferItem>();
                var driversData = visit.SelectedDrivers.Split(" | ");

                for (int i = 0; i < driversData.Length; i++)
                {
                    var driverData = driversData[i];
                    var parts = driverData.Split(" - ");

                    if (parts.Length >= 3)
                    {
                        var name = parts[0];
                        var phone = parts[1];
                        var priceText = parts[2];

                        decimal price = 0;
                        var priceMatch = System.Text.RegularExpressions.Regex.Match(priceText, @"\d+");
                        if (priceMatch.Success)
                        {
                            decimal.TryParse(priceMatch.Value, out price);
                        }

                        string status = "مقبول";
                        bool isWinner = false;
                        if (priceText.Contains("🏆 فائز"))
                        {
                            status = "فائز";
                            isWinner = true;
                        }
                        else if (priceText.Contains("❌ مرفوض"))
                        {
                            status = "مرفوض";
                        }

                        offers.Add(new PriceOfferItem
                        {
                            SerialNumber = i + 1,
                            DriverName = name,
                            PhoneNumber = phone,
                            OfferedPrice = price,
                            Status = status,
                            IsWinner = isWinner
                        });
                    }
                }

                return offers.Count > 0 ? offers : null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحليل بيانات السائقين: {ex.Message}");
                return null;
            }
        }

        private async Task LoadWinnerDriverData(FieldVisit visit)
        {
            try
            {
                var winnerOffer = ReportData.PriceOffers.FirstOrDefault(o => o.IsWinner);

                if (winnerOffer != null)
                {
                    var driverDetails = await GetDriverDetailsByName(winnerOffer.DriverName);

                    if (driverDetails != null)
                    {
                        ReportData.WinnerDriver = new WinnerDriverInfo
                        {
                            DriverName = driverDetails.Name,
                            NationalId = driverDetails.DriverCode ?? "غير محدد", // استخدام DriverCode بدلاً من NationalId
                            IssueLocation = "غير محدد", // لا يوجد في Driver model
                            IssueDate = "غير محدد" // لا يوجد في Driver model
                        };
                    }
                    else
                    {
                        ReportData.WinnerDriver = new WinnerDriverInfo
                        {
                            DriverName = winnerOffer.DriverName,
                            NationalId = "غير محدد",
                            IssueLocation = "غير محدد",
                            IssueDate = "غير محدد"
                        };
                    }
                }
                else
                {
                    ReportData.WinnerDriver = new WinnerDriverInfo
                    {
                        DriverName = "لم يتم تحديد السائق الفائز",
                        NationalId = "غير محدد",
                        IssueLocation = "غير محدد",
                        IssueDate = "غير محدد"
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات السائق الفائز: {ex.Message}");
                ReportData.WinnerDriver = new WinnerDriverInfo
                {
                    DriverName = "خطأ في التحميل",
                    NationalId = "غير محدد",
                    IssueLocation = "غير محدد",
                    IssueDate = "غير محدد"
                };
            }
        }

        private async Task<Driver> GetDriverDetailsByName(string driverName)
        {
            try
            {
                using var context = new ApplicationDbContext();
                return await context.Drivers
                    .FirstOrDefaultAsync(d => d.Name.Contains(driverName) || driverName.Contains(d.Name));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب تفاصيل السائق: {ex.Message}");
                return null;
            }
        }

        private async Task LoadSelectedVehicleData(FieldVisit visit)
        {
            try
            {
                var offers = await _databaseService.GetVisitOffersAsync(visit.VisitNumber);
                var selectedOffer = offers?.FirstOrDefault(o => o.IsSelected);

                if (selectedOffer != null)
                {
                    ReportData.SelectedDriverName = selectedOffer.DriverName;
                    ReportData.SelectedDriverPhone = selectedOffer.PhoneNumber;
                    ReportData.VehicleType = selectedOffer.VehicleType ?? "غير محدد";
                    ReportData.VehicleModel = "غير محدد";
                    ReportData.VehicleColor = "غير محدد";
                    ReportData.PlateNumber = selectedOffer.VehicleNumber ?? "غير محدد";
                }
                else if (ReportData.PriceOffers.Count > 0 &&
                         ReportData.PriceOffers.First().DriverName != "لا توجد عروض أسعار")
                {
                    var firstOffer = ReportData.PriceOffers.First();
                    ReportData.SelectedDriverName = firstOffer.DriverName;
                    ReportData.SelectedDriverPhone = firstOffer.PhoneNumber;
                    ReportData.VehicleType = "غير محدد";
                    ReportData.VehicleModel = "غير محدد";
                    ReportData.VehicleColor = "غير محدد";
                    ReportData.PlateNumber = "غير محدد";
                }
                else
                {
                    ReportData.SelectedDriverName = "لم يتم اختيار سائق";
                    ReportData.SelectedDriverPhone = "غير محدد";
                    ReportData.VehicleType = "غير محدد";
                    ReportData.VehicleModel = "غير محدد";
                    ReportData.VehicleColor = "غير محدد";
                    ReportData.PlateNumber = "غير محدد";
                }
            }
            catch (Exception ex)
            {
                ReportData.SelectedDriverName = "خطأ في تحميل البيانات";
                ReportData.SelectedDriverPhone = "غير محدد";
                ReportData.VehicleType = "غير محدد";
                ReportData.VehicleModel = "غير محدد";
                ReportData.VehicleColor = "غير محدد";
                ReportData.PlateNumber = "غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading vehicle data: {ex.Message}");
            }
        }

        private void LoadSignaturesData(FieldVisit visit)
        {
            try
            {
                ReportData.TaskManagerName = "مسئول المهمة";
                ReportData.MovementResponsibleName = "مسئول الحركة";
                ReportData.BranchManagerName = "مدير الفرع";
                ReportData.BranchManagerTitle = "مدير الفرع";
            }
            catch (Exception ex)
            {
                ReportData.TaskManagerName = "غير محدد";
                ReportData.MovementResponsibleName = "غير محدد";
                ReportData.BranchManagerName = "غير محدد";
                ReportData.BranchManagerTitle = "غير محدد";
                System.Diagnostics.Debug.WriteLine($"Error loading signatures: {ex.Message}");
            }
        }

        private void GenerateMessageText()
        {
            if (SelectedVisit == null) return;

            try
            {
                if (string.IsNullOrWhiteSpace(ReportData.SelectedDriverName) ||
                    ReportData.SelectedDriverName == "لم يتم اختيار سائق" ||
                    ReportData.SelectedDriverName == "خطأ في تحميل البيانات")
                {
                    ReportData.MessageText = $"تم استطلاع أسعار النقل للمشاريع المذكورة أعلاه لمدة {ReportData.DaysCount} يوم " +
                                           $"من تاريخ {ReportData.DepartureDate} إلى {ReportData.ReturnDate}. " +
                                           $"لم يتم اختيار سائق بعد.";
                }
                else
                {
                    ReportData.MessageText = $"تم استطلاع أسعار النقل للمشاريع المذكورة أعلاه لمدة {ReportData.DaysCount} يوم " +
                                           $"من تاريخ {ReportData.DepartureDate} إلى {ReportData.ReturnDate}، " +
                                           $"وقد تم اختيار السائق {ReportData.SelectedDriverName} " +
                                           $"برقم الهاتف {ReportData.SelectedDriverPhone}.";
                }
            }
            catch (Exception ex)
            {
                ReportData.MessageText = "خطأ في إنشاء نص الرسالة";
                System.Diagnostics.Debug.WriteLine($"Error generating message: {ex.Message}");
            }
        }

        private void LoadWinnerDriverMessage(FieldVisit visit)
        {
            try
            {
                if (!string.IsNullOrEmpty(visit.SelectedDrivers))
                {
                    var driversData = visit.SelectedDrivers.Split(" | ");

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 3 && parts[2].Contains("🏆 فائز"))
                        {
                            var winnerName = parts[0];
                            var messageText = GenerateWinnerMessage(winnerName, visit);
                            ReportData.WinnerDriverMessage = messageText;
                            System.Diagnostics.Debug.WriteLine($"✅ تم توليد نص الرسالة للسائق الفائز: {winnerName}");
                            return;
                        }
                    }
                }

                ReportData.WinnerDriverMessage = "لم يتم تحديد السائق الفائز بعد";
                System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد سائق فائز محدد");
            }
            catch (Exception ex)
            {
                ReportData.WinnerDriverMessage = "خطأ في تحميل نص الرسالة";
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل نص الرسالة: {ex.Message}");
            }
        }

        private string GenerateWinnerMessage(string driverName, FieldVisit visit)
        {
            try
            {
                var itineraryText = "";
                if (visit.Itinerary?.Count > 0)
                {
                    itineraryText = string.Join(" - ", visit.Itinerary);
                }

                var visitorsText = "";
                if (visit.Visitors?.Count > 0)
                {
                    visitorsText = string.Join(" و ", visit.Visitors.Select(v => v.OfficerName));
                }

                var startDate = visit.DepartureDate.ToString("dd/MM/yyyy");
                var endDate = visit.ReturnDate.ToString("dd/MM/yyyy");

                var messageText = $@"الأخ/{driverName} المحترم،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({visit.DaysCount} يوم) مع الأخ: {visitorsText}
في المناطق التالية:
{itineraryText}

📅 تاريخ النزول: {startDate} - 📅 تاريخ العودة: {endDate}
وشكراً - ادارة حركة السائقين بالصندوق الاجتماعي فرع ذمار";

                return messageText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في توليد نص الرسالة: {ex.Message}");
                return $"الأخ/{driverName} المحترم، تم اختياركم للزيارة الميدانية رقم {visit.VisitNumber}";
            }
        }

        private async Task LoadDocumentationImages(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🖼️ بدء تحميل صور التوثيق للزيارة: {visit.VisitNumber}");

                using var context = new Data.ApplicationDbContext();
                var documentation = await context.MessageDocumentations
                    .FirstOrDefaultAsync(d => d.VisitNumber == visit.VisitNumber);

                if (documentation != null)
                {
                    ReportData.DocumentationImage1 = documentation.ImagePath1 ?? "";
                    ReportData.DocumentationImage2 = documentation.ImagePath2 ?? "";
                    ReportData.DocumentationImage3 = documentation.ImagePath3 ?? "";
                    ReportData.DocumentationImage4 = documentation.ImagePath4 ?? "";

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل صور التوثيق");
                }
                else
                {
                    ReportData.DocumentationImage1 = "";
                    ReportData.DocumentationImage2 = "";
                    ReportData.DocumentationImage3 = "";
                    ReportData.DocumentationImage4 = "";

                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد صور توثيق لهذه الزيارة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل صور التوثيق: {ex.Message}");

                ReportData.DocumentationImage1 = "";
                ReportData.DocumentationImage2 = "";
                ReportData.DocumentationImage3 = "";
                ReportData.DocumentationImage4 = "";
            }
        }

        #endregion
    }
}
