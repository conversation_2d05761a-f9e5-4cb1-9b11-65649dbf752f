﻿#pragma checksum "..\..\..\..\Views\BatchImportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AAFC296C0430BD6B4F4971A4E6814BD784E39149"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// BatchImportWindow
    /// </summary>
    public partial class BatchImportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 127 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultIndexTextBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IndexPatternTextBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DelayTextBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeSubfoldersCheckBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StopOnErrorCheckBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid FilesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartImportButton;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopImportButton;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewResultsButton;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Views\BatchImportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;component/views/batchimportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\BatchImportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 101 "..\..\..\..\Views\BatchImportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 107 "..\..\..\..\Views\BatchImportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectFolderButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 113 "..\..\..\..\Views\BatchImportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearListButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DefaultIndexTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.IndexPatternTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.DelayTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.IncludeSubfoldersCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.StopOnErrorCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.FilesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 10:
            this.StartImportButton = ((System.Windows.Controls.Button)(target));
            
            #line 211 "..\..\..\..\Views\BatchImportWindow.xaml"
            this.StartImportButton.Click += new System.Windows.RoutedEventHandler(this.StartImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StopImportButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Views\BatchImportWindow.xaml"
            this.StopImportButton.Click += new System.Windows.RoutedEventHandler(this.StopImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ViewResultsButton = ((System.Windows.Controls.Button)(target));
            
            #line 225 "..\..\..\..\Views\BatchImportWindow.xaml"
            this.ViewResultsButton.Click += new System.Windows.RoutedEventHandler(this.ViewResultsButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\Views\BatchImportWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

