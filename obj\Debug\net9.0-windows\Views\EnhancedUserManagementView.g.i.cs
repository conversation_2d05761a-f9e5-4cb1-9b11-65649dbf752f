﻿#pragma checksum "..\..\..\..\Views\EnhancedUserManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "33CD6A97FC6296FD99F8E341AFFF2D94A87C301D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// EnhancedUserManagementView
    /// </summary>
    public partial class EnhancedUserManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 93 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalUsersText;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveUsersText;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddUserButton;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatTotalUsers;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatActiveUsers;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatAdminUsers;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatRecentActivity;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearSearchButton;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RoleFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SortComboBox;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel UsersPanel;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NoUsersPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V1.0.0.0;component/views/enhancedusermanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalUsersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ActiveUsersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.AddUserButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.AddUserButton.Click += new System.Windows.RoutedEventHandler(this.AddUserButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 124 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.StatTotalUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.StatActiveUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.StatAdminUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.StatRecentActivity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 223 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 224 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 225 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ClearSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.ClearSearchButton.Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.RoleFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 234 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.RoleFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RoleFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 244 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SortComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 254 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            this.SortComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SortComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.UsersPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.LoadingPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.NoUsersPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 19:
            
            #line 289 "..\..\..\..\Views\EnhancedUserManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddUserButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

